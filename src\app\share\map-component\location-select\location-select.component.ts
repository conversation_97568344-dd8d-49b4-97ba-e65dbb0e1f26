import { AfterViewInit, Component, HostListener, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Coordinate } from 'ol/coordinate';
import { MapComponent } from '../map/map.component';
import { KeyPointInfo } from 'src/app/execut/class/key-point';
import { FormGroup } from '@angular/forms';
import { DetailsMode } from 'src/app/@core/base/environment';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { ExecutService } from 'src/app/execut/execut.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { CoordinateSystemService } from '../service/coordinate-system.service';
import { CoordinateSystem } from '../map/layer.config';

// 导入新创建的服务
import { LocationCollectionService, LocationCollectionState, CollectedLocation } from './services/location-collection.service';
import { KeyPointFormService, FormState } from './services/keypoint-form.service';
import { StepControlService, StepState, StepType } from './services/step-control.service';

@Component({
  selector: 'app-location-select',
  templateUrl: './location-select.component.html',
  styleUrls: ['./location-select.component.scss'],
})
export class LocationSelectComponent implements AfterViewInit, OnInit, OnDestroy {
  @ViewChild('ostMap', { static: false }) ostMap!: MapComponent;
  @Input() coordinate: Coordinate = [116.4047470, 39.9069387];
  @Input() taskCode!: string;
  @Input() modelInfo: KeyPointInfo = new KeyPointInfo();
  @Input() modelMode!: DetailsMode;

  layerId: string[] = ['inspect_point'];
  DetailsMode = DetailsMode;

  // 表单相关
  infoFormGroup!: FormGroup;
  isItRaining = false;

  // UI状态（从服务中获取）
  showMap = true;
  currentStep: StepType = 'location';
  showMask: boolean = true;
  isCollecting: boolean = false;
  countdown: number = 5;
  locationConfirmed: boolean = false;
  accuracyStatus: 'good' | 'poor' | 'unknown' = 'unknown';
  showAccuracyTip: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(
    private modalController: ModalController,
    private userSer: UserInfoService,
    private netSer: ExecutService,
    private toastSer: ToastService,
    private coordinateSystemService: CoordinateSystemService,
    // 注入新创建的服务
    private locationCollectionService: LocationCollectionService,
    private keyPointFormService: KeyPointFormService,
    private stepControlService: StepControlService
  ) { }

  ngOnInit(): void {
    this.initializeData();
    this.createForm();
    this.subscribeToServices();
  }

  ngAfterViewInit(): void {
    this.initCenterPoint();
  }

  /**
   * 初始化数据
   */
  private initializeData(): void {
    if (this.modelMode === DetailsMode.ADD) {
      this.modelInfo.depCode = this.userSer.depCode;
    } else {
      // 编辑模式下，设置坐标和雨天状态
      this.coordinate = this.modelInfo.pointGeom;
      this.isItRaining = this.keyPointFormService.convertStringToBoolean(this.modelInfo.isItRaining);
    }

    // 初始化步骤控制
    this.stepControlService.initializeSteps(this.modelMode);

    // 初始化表单数据
    this.keyPointFormService.initializeFormData(this.modelInfo, this.modelMode);
  }

  /**
   * 创建表单
   */
  private createForm(): void {
    this.infoFormGroup = this.keyPointFormService.createForm(this.modelInfo, this.taskCode);
  }

  /**
   * 订阅服务状态变化
   */
  private subscribeToServices(): void {
    // 订阅步骤控制状态
    this.stepControlService.getStepState()
      .pipe(takeUntil(this.destroy$))
      .subscribe((stepState: StepState) => {
        this.currentStep = stepState.currentStep;
        this.locationConfirmed = stepState.locationConfirmed;
        this.showMap = stepState.showMap;
      });

    // 订阅位置采集状态
    this.locationCollectionService.getState()
      .pipe(takeUntil(this.destroy$))
      .subscribe((collectionState: LocationCollectionState) => {
        this.isCollecting = collectionState.isCollecting;
        this.countdown = collectionState.countdown;
        this.showMask = collectionState.showMask;
        this.accuracyStatus = collectionState.accuracyStatus;
        this.showAccuracyTip = collectionState.showAccuracyTip;
      });

    // 订阅位置采集完成事件
    this.locationCollectionService.getCollectionComplete()
      .pipe(takeUntil(this.destroy$))
      .subscribe((location: CollectedLocation | null) => {
        this.handleCollectionComplete(location);
      });

    // 订阅雨天状态变化
    this.keyPointFormService.getIsItRaining()
      .pipe(takeUntil(this.destroy$))
      .subscribe((isItRaining: boolean) => {
        this.isItRaining = isItRaining;
      });
  }

  /**
   * 处理位置采集完成
   */
  private handleCollectionComplete(location: CollectedLocation | null): void {
    if (location) {
      // 采集成功，更新坐标并设置到地图
      this.coordinate = location.coordinate;
      this.ostMap.setCurrentLocation(this.coordinate, location.accuracy);

      const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();
      console.log('✅ 采集完成，最佳定位点:', {
        坐标: location.coordinate,
        精度: location.accuracy,
        坐标系: currentCRS,
        原始坐标: location.originalCoordinate,
        是否转换: location.transformResult ? '是' : '否'
      });

      // 根据精度状态给出不同的提示
      if (this.accuracyStatus === 'good') {
        const crsName = this.coordinateSystemService.getCoordinateSystemDisplayName(currentCRS);
        this.toastSer.presentToast(`位置采集成功（${crsName}），精度良好，请确认位置`, 'success');
      }
    } else {
      // 采集失败
      this.toastSer.presentToast('定位采集失败，请重试', 'danger');
      console.error('❌ 定位采集失败，未获取到有效定位');
    }
  }

  /**
   * 地图初始化中心点（支持坐标系转换）
   */
  private initCenterPoint(): void {
    if (this.isValidCoordinate(this.coordinate)) {
      // 获取当前地图坐标系
      const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();

      // 如果是编辑模式且有坐标系信息，检查是否需要转换
      let finalCoordinate = this.coordinate;

      if (this.modelMode === DetailsMode.EDITE && (this.modelInfo as any).coordinateSystem) {
        const sourceCRS = (this.modelInfo as any).coordinateSystem as CoordinateSystem;

        // 如果数据坐标系与当前地图坐标系不同，进行转换
        if (sourceCRS !== currentCRS) {
          const transformResult = this.coordinateSystemService.transformCoordinate(
            this.coordinate,
            sourceCRS,
            currentCRS
          );
          finalCoordinate = transformResult.coordinate;

          console.log('🔄 编辑模式坐标转换:', {
            原始坐标: this.coordinate,
            源坐标系: sourceCRS,
            目标坐标系: currentCRS,
            转换后坐标: finalCoordinate
          });
        }
      }

      this.coordinate = finalCoordinate;
      this.ostMap.setCurrentLocation(finalCoordinate);
    } else {
      // 如果没有有效坐标，不进行定位，等待用户手动采集
      console.log('🎯 没有有效坐标，等待用户手动采集');
    }
  }

  /**
   * 检查坐标是否有效
   * @param coordinate 坐标
   * @returns 是否有效
   */
  private isValidCoordinate(coordinate: any): boolean {
    return coordinate &&
      !(Array.isArray(coordinate) && coordinate[0] === 0 && coordinate[1] === 0);
  }

  /**
   * 切换地图/表单视图
   */
  segmentChanged(ev: any): void {
    const segment = ev.detail.value;
    const result = this.stepControlService.handleSegmentChange(segment);

    if (!result.success && result.message) {
      this.toastSer.presentToast(result.message, 'warning');
    }
  }

  /**
   * 部门值改变
   */
  valueChange(ev: { name: string }): void {
    this.keyPointFormService.updateDepartment(ev);
  }

  /**
   * 确定提交
   */
  onConfirm(): void {
    if (this.currentStep === 'location') {
      // 位置选择步骤，确认位置
      this.confirmLocation();
    } else if (this.currentStep === 'info') {
      // 基础信息步骤，提交表单
      if (!this.keyPointFormService.validateForm()) {
        const errors = this.keyPointFormService.getRequiredFieldErrors();
        this.toastSer.presentToast(errors.length > 0 ? errors[0] : '请填写必填项', 'warning');
        return;
      }
      const formData = this.prepareFormData();
      this.onSubmit(formData);
    }
  }

  /**
   * 确认位置
   */
  confirmLocation(): void {
    if (!this.isValidCoordinate(this.coordinate)) {
      this.toastSer.presentToast('请先采集位置信息', 'warning');
      return;
    }

    // 使用步骤控制服务确认位置
    this.stepControlService.confirmLocation();

    // 切换到信息填写步骤
    const success = this.stepControlService.switchToInfoStep();
    if (success) {
      this.toastSer.presentToast('位置确认成功，请填写基础信息', 'success');
    }
  }

  /**
   * 准备提交数据（包含坐标系信息）
   */
  private prepareFormData(): KeyPointInfo {
    // 获取当前地图中心点坐标
    const centerCoordinate = this.ostMap.view.getCenter();

    // 使用表单服务准备数据
    return this.keyPointFormService.prepareSubmitData(this.modelInfo, centerCoordinate);
  }

  /**
   * 保存表单
   */
  private async onSubmit(formData: KeyPointInfo): Promise<void> {
    const operate = this.modelMode === DetailsMode.ADD
      ? this.netSer.addKeyPoint(formData)
      : this.netSer.updateKeyPoint(formData);

    try {
      const res = await operate.pipe(takeUntil(this.destroy$)).toPromise();
      if (res?.code === 0) {
        this.toastSer.presentToast('操作成功', 'success');
        await this.modalController.dismiss({ centerCoord: formData.pointGeom }, 'confirm');
      } else {
        this.toastSer.presentToast(res?.msg || '操作失败', 'danger');
      }
    } catch (error) {
      console.error('Error submitting form data:', error);
      this.toastSer.presentToast('网络错误，请稍后重试', 'danger');
    }
  }

  /**
   * 回退
   */
  goBack(): void {
    this.modalController.dismiss();
  }

  /**
   * 手机左滑回退
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton(event: CustomEvent): void {
    event.detail.register(100, async () => {
      event.stopImmediatePropagation();
      await this.modalController.dismiss();
    });
  }

  /**
   * 开始采集按钮点击
   */
  startCollect(): void {
    this.locationCollectionService.startCollection();
  }



  /**
   * 返回位置选择步骤
   */
  backToLocationStep(): void {
    this.stepControlService.backToLocationStep();
    this.locationCollectionService.resetCollectionState();
  }

  /**
   * 关闭精度提示
   */
  closeAccuracyTip(): void {
    this.locationCollectionService.closeAccuracyTip();
  }

  /**
   * 获取动态标题
   */
  getDynamicTitle(): string {
    return this.stepControlService.getDynamicTitle(this.modelMode);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // 清理服务资源
    this.locationCollectionService.destroy();
    this.keyPointFormService.destroy();
    this.stepControlService.destroy();
  }
}