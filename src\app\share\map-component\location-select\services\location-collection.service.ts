import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { Geolocation } from '@ionic-native/geolocation/ngx';
import { Coordinate } from 'ol/coordinate';
import { CoordinateSystemService, CoordinateTransformResult } from '../../service/coordinate-system.service';
import { CoordinateSystem } from '../../map/layer.config';

/**
 * 位置采集状态
 */
export interface LocationCollectionState {
  isCollecting: boolean;
  countdown: number;
  showMask: boolean;
  accuracyStatus: 'good' | 'poor' | 'unknown';
  showAccuracyTip: boolean;
}

/**
 * 采集的位置信息
 */
export interface CollectedLocation {
  coordinate: Coordinate;
  accuracy: number;
  originalCoordinate?: Coordinate;
  coordinateSystem?: CoordinateSystem;
  transformResult?: CoordinateTransformResult | null;
}

/**
 * 位置采集服务
 * 负责GPS定位、坐标转换、精度评估等功能
 */
@Injectable({
  providedIn: 'root'
})
export class LocationCollectionService {
  // 采集状态
  private state$ = new BehaviorSubject<LocationCollectionState>({
    isCollecting: false,
    countdown: 5,
    showMask: true,
    accuracyStatus: 'unknown',
    showAccuracyTip: false
  });

  // 采集完成事件
  private collectionComplete$ = new Subject<CollectedLocation | null>();

  // 采集的位置数据
  private collectedLocations: CollectedLocation[] = [];
  private bestLocation: CollectedLocation | null = null;

  // 采集配置
  private readonly COLLECTION_DURATION = 5; // 采集持续时间（秒）
  private readonly COLLECTION_INTERVAL = 2000; // 采集间隔（毫秒）
  private readonly ACCURACY_THRESHOLD = 10; // 精度阈值（米）

  constructor(
    private geolocation: Geolocation,
    private coordinateSystemService: CoordinateSystemService
  ) {}

  /**
   * 获取采集状态
   */
  getState(): Observable<LocationCollectionState> {
    return this.state$.asObservable();
  }

  /**
   * 获取当前状态值
   */
  getCurrentState(): LocationCollectionState {
    return this.state$.value;
  }

  /**
   * 获取采集完成事件
   */
  getCollectionComplete(): Observable<CollectedLocation | null> {
    return this.collectionComplete$.asObservable();
  }

  /**
   * 开始位置采集
   */
  startCollection(): void {
    console.log('🚀 开始关键点定位采集流程');
    
    // 重置状态
    this.resetCollectionState();
    
    // 更新状态
    this.updateState({
      isCollecting: true,
      countdown: this.COLLECTION_DURATION,
      showMask: true,
      accuracyStatus: 'unknown',
      showAccuracyTip: false
    });

    // 开始采集流程
    this.startLocationCollection();
    this.startCountdown();
  }

  /**
   * 停止位置采集
   */
  stopCollection(): void {
    this.updateState({
      isCollecting: false
    });
  }

  /**
   * 重置采集状态
   */
  resetCollectionState(): void {
    this.collectedLocations = [];
    this.bestLocation = null;
    this.updateState({
      isCollecting: false,
      countdown: this.COLLECTION_DURATION,
      showMask: true,
      accuracyStatus: 'unknown',
      showAccuracyTip: false
    });
  }

  /**
   * 关闭精度提示
   */
  closeAccuracyTip(): void {
    this.updateState({
      showAccuracyTip: false
    });
  }

  /**
   * 隐藏遮罩
   */
  hideMask(): void {
    this.updateState({
      showMask: false
    });
  }

  /**
   * 获取最佳位置
   */
  getBestLocation(): CollectedLocation | null {
    return this.bestLocation;
  }

  /**
   * 开始定位采集流程
   */
  private startLocationCollection(): void {
    // 立即开始第一次定位
    this.collectLocation();

    // 每2秒采集一次定位，持续5秒（总共3次定位）
    const collectionInterval = setInterval(() => {
      if (this.getCurrentState().isCollecting) {
        this.collectLocation();
      } else {
        clearInterval(collectionInterval);
      }
    }, this.COLLECTION_INTERVAL);
  }

  /**
   * 采集单次定位信息
   */
  private async collectLocation(): Promise<void> {
    try {
      console.log('🎯 开始关键点定位采集（使用Geolocation插件）');

      // 使用Geolocation插件进行定位，避免与巡检轨迹的BackgroundGeolocation冲突
      const options = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      };

      const position = await this.geolocation.getCurrentPosition(options);
      const coords = position.coords;

      console.log('📍 关键点定位结果:', {
        accuracy: coords.accuracy,
        longitude: coords.longitude,
        latitude: coords.latitude
      });

      // 转换为地图坐标格式
      const coordinate: Coordinate = [coords.longitude, coords.latitude];

      // 处理定位结果
      this.handleLocationResult(coordinate, coords.accuracy);

    } catch (error) {
      console.error('❌ 关键点定位采集失败:', error);
    }
  }

  /**
   * 处理定位结果（支持坐标系转换）
   */
  private handleLocationResult(coordinate: Coordinate, accuracy: number): void {
    console.log('🎯 处理定位结果:', { coordinate, accuracy });

    // 获取当前地图使用的坐标系
    const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();
    console.log('🗺️ 当前地图坐标系:', currentCRS);

    // GPS通常返回WGS84坐标，需要根据当前地图坐标系进行转换
    let finalCoordinate = coordinate;
    let transformResult: CoordinateTransformResult | null = null;

    // 如果当前使用的是GCJ02坐标系（高德地图），需要进行坐标转换
    if (currentCRS === CoordinateSystem.GCJ02) {
      transformResult = this.coordinateSystemService.transformCoordinate(
        coordinate,
        CoordinateSystem.CGCS2000, // GPS坐标接近CGCS2000
        CoordinateSystem.GCJ02
      );
      finalCoordinate = transformResult.coordinate;
      console.log('🔄 坐标转换:', {
        原始坐标: coordinate,
        转换后坐标: finalCoordinate,
        转换精度: transformResult.accuracy
      });
    }

    const location: CollectedLocation = {
      coordinate: finalCoordinate,
      accuracy,
      originalCoordinate: coordinate,
      coordinateSystem: currentCRS,
      transformResult
    };

    this.collectedLocations.push(location);
    console.log('📍 已采集定位数量:', this.collectedLocations.length);

    // 更新最佳定位点（精度最高）
    if (!this.bestLocation || accuracy < this.bestLocation.accuracy) {
      this.bestLocation = location;
      console.log('⭐ 更新最佳定位点:', {
        coordinate: finalCoordinate,
        accuracy,
        coordinateSystem: currentCRS
      });
    } else {
      console.log('📊 当前定位精度不如最佳定位点，跳过');
    }
  }

  /**
   * 开始倒计时
   */
  private startCountdown(): void {
    const countdownInterval = setInterval(() => {
      const currentState = this.getCurrentState();
      if (currentState.countdown > 1 && currentState.isCollecting) {
        this.updateState({
          countdown: currentState.countdown - 1
        });
      } else {
        clearInterval(countdownInterval);
        this.finishCollection();
      }
    }, 1000);
  }

  /**
   * 完成采集
   */
  private finishCollection(): void {
    this.updateState({
      isCollecting: false
    });

    if (this.bestLocation) {
      const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();
      console.log('✅ 采集完成，最佳定位点:', {
        坐标: this.bestLocation.coordinate,
        精度: this.bestLocation.accuracy,
        坐标系: currentCRS,
        原始坐标: this.bestLocation.originalCoordinate,
        是否转换: this.bestLocation.transformResult ? '是' : '否'
      });

      // 评估精度并设置状态
      const accuracyStatus = this.evaluateAccuracy(this.bestLocation.accuracy);
      const showAccuracyTip = accuracyStatus === 'poor';

      this.updateState({
        accuracyStatus,
        showAccuracyTip,
        showMask: accuracyStatus === 'good' // 精度好的话直接隐藏遮罩
      });

      // 发送采集完成事件
      this.collectionComplete$.next(this.bestLocation);
    } else {
      console.error('❌ 定位采集失败，未获取到有效定位');
      
      // 重置状态，允许重新采集
      this.resetCollectionState();
      
      // 发送采集失败事件
      this.collectionComplete$.next(null);
    }
  }

  /**
   * 评估定位精度
   */
  private evaluateAccuracy(accuracy: number): 'good' | 'poor' {
    return accuracy <= this.ACCURACY_THRESHOLD ? 'good' : 'poor';
  }

  /**
   * 更新状态
   */
  private updateState(partialState: Partial<LocationCollectionState>): void {
    const currentState = this.getCurrentState();
    this.state$.next({
      ...currentState,
      ...partialState
    });
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.state$.complete();
    this.collectionComplete$.complete();
  }
}
