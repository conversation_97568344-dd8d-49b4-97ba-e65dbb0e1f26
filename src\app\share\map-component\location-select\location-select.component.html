<ion-header class="sticky" [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start" class="title-start-back">
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <!-- 动态标题 -->
    <ion-title>
      {{ getDynamicTitle() }}
    </ion-title>
  </ion-toolbar>
</ion-header>
<ion-header>
  <ion-segment scrollable value="map" (ionChange)="segmentChanged($event)">
    <ion-segment-button value="map" layout="icon-start">
      <ion-label>属性位置</ion-label>
      <ion-icon name="pin" style="margin-top: 4px;margin-bottom: 4px;"></ion-icon>
      <ion-badge *ngIf="locationConfirmed" color="success" style="position: absolute; top: 2px; right: 2px; font-size: 10px;">✓</ion-badge>
    </ion-segment-button>
    <ion-segment-button value="basic" layout="icon-start" [disabled]="!locationConfirmed">
      <ion-label [style.opacity]="locationConfirmed ? '1' : '0.5'">基础信息</ion-label>
      <ion-icon name="reader-outline" style="margin-top: 4px;margin-bottom: 4px;" [style.opacity]="locationConfirmed ? '1' : '0.5'"></ion-icon>
    </ion-segment-button>
  </ion-segment>
</ion-header>
<ion-content>
  <!-- 位置采集遮罩 -->
  <div class="collect-mask" *ngIf="showMask && currentStep === 'location'">
    <div class="mask-content">
      <div class="collect-info">
        <ion-icon name="location-outline" style="font-size: 48px; color: var(--ion-color-primary); margin-bottom: 16px;"></ion-icon>
        <h3>位置采集</h3>
        <p>点击开始采集按钮获取精确位置信息</p>
      </div>
      <ion-button *ngIf="!isCollecting" (click)="startCollect()" expand="block" color="primary" size="large">
        <ion-icon name="navigate-outline" slot="start"></ion-icon>
        开始采集
      </ion-button>
      <div *ngIf="isCollecting" class="countdown-container">
        <div class="countdown">{{ countdown }}</div>
        <p>正在采集位置信息...</p>
      </div>
    </div>
  </div>
  <!-- 属性位置 -->
  <div [hidden]="!showMap">
    <ost-map #ostMap [zoom]="6" [center]="coordinate" [layerIds]="layerId" [showLocationInfo]="true"></ost-map>
    <ost-map-layout>
      <ost-layout-item align="center">
        <ion-icon name="add-outline"
          [color]="accuracyStatus === 'good' ? 'success' : accuracyStatus === 'poor' ? 'danger' : 'danger'"
          style="font-size: 38px;">
        </ion-icon>
      </ost-layout-item>
    </ost-map-layout>

    <!-- 精度提示条 - 极简设计 -->
    <div class="accuracy-tip-banner" *ngIf="showAccuracyTip && !showMask">
      <div class="tip-content">
        <ion-icon name="warning-outline"></ion-icon>
        <span>当前偏差较大，建议刷新或手动调整位置</span>
        <!-- <ion-button fill="clear" size="small" (click)="closeAccuracyTip()">
          <ion-icon name="close-outline"></ion-icon>
        </ion-button> -->
      </div>
    </div>

    <ion-button expand="block" color="primary"
      style="position: fixed; bottom: 10vh; left: 10vw; width: 80vw;"
      (click)="confirmLocation()">
      确认位置
    </ion-button>
  </div>
  <!-- 基础信息 -->
  <div [hidden]="showMap">
    <ost-form-list [formGroup]="infoFormGroup">
      <ost-form-item>
        <ion-label>部门&nbsp;:</ion-label>
        <ost-input-search
          placeholder="点击选择部门"
          (valueChange)="valueChange($event)"
          [name]="modelInfo.depName"
          formControlName="depCode" slot="end"
        >
          <search-source-org [depCode]="modelInfo.depCode"></search-source-org>
        </ost-input-search>
        <ost-error errorCode="required">部门名称不能为空</ost-error>
      </ost-form-item>

      <ost-form-item>
        <ion-label>关键点名称&nbsp;:</ion-label>
        <ion-input formControlName="pointName"></ion-input>
        <ost-error errorCode="required">关键点名称不能为空</ost-error>
      </ost-form-item>

      <ost-form-item>
        <ion-label>巡检方式&nbsp;:</ion-label>
        <ost-quick-option-select
        formControlName="inspectionMethod"
        [labelValue]="'dictValue'"
        [labelName]="'dictValue'"
        [modalType]="modelMode"
        interfaceUrl="/work-inspect/api/v2/inspect/dict/msg/list?dictCode=InspectionMethod"
        ></ost-quick-option-select>
      </ost-form-item>

      <ost-form-item [required]="false">
        <ion-label>缓冲范围(米)&nbsp;:</ion-label>
        <ion-input type="number" formControlName="bufferRange"></ion-input>
      </ost-form-item>
    </ost-form-list>
    <ion-item-divider lines="none">
      <ion-label>雨天不巡查&nbsp;:</ion-label>
      <ion-toggle slot="end" [(ngModel)]="isItRaining"></ion-toggle>
    </ion-item-divider>

    <ion-button expand="block" color="primary" 
      style="position: relative; margin: 8vw; margin-top: 10vh;"
      (click)="onConfirm()">
      提交
    </ion-button>
  </div>

</ion-content>
