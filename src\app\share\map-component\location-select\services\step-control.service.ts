import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { DetailsMode } from 'src/app/@core/base/environment';

/**
 * 步骤类型
 */
export type StepType = 'location' | 'info';

/**
 * 步骤状态
 */
export interface StepState {
  currentStep: StepType;
  locationConfirmed: boolean;
  showMap: boolean;
  canSwitchToInfo: boolean;
}

/**
 * Segment值类型
 */
export type SegmentValue = 'map' | 'basic';

/**
 * 步骤控制服务
 * 负责步骤切换、状态管理等功能
 */
@Injectable({
  providedIn: 'root'
})
export class StepControlService {
  // 步骤状态
  private stepState$ = new BehaviorSubject<StepState>({
    currentStep: 'location',
    locationConfirmed: false,
    showMap: true,
    canSwitchToInfo: false
  });

  constructor() {}

  /**
   * 获取步骤状态
   */
  getStepState(): Observable<StepState> {
    return this.stepState$.asObservable();
  }

  /**
   * 获取当前步骤状态值
   */
  getCurrentStepState(): StepState {
    return this.stepState$.value;
  }

  /**
   * 初始化步骤状态
   */
  initializeSteps(modelMode: DetailsMode): void {
    if (modelMode === DetailsMode.ADD) {
      // 新增模式下，从位置选择步骤开始
      this.updateStepState({
        currentStep: 'location',
        locationConfirmed: false,
        showMap: true,
        canSwitchToInfo: false
      });
    } else {
      // 编辑模式下，直接跳到信息填写步骤
      this.updateStepState({
        currentStep: 'info',
        locationConfirmed: true,
        showMap: false,
        canSwitchToInfo: true
      });
    }
  }

  /**
   * 确认位置
   */
  confirmLocation(): void {
    this.updateStepState({
      locationConfirmed: true,
      canSwitchToInfo: true
    });
  }

  /**
   * 切换到信息填写步骤
   */
  switchToInfoStep(): boolean {
    const currentState = this.getCurrentStepState();
    
    if (!currentState.locationConfirmed) {
      return false; // 位置未确认，不能切换
    }

    this.updateStepState({
      currentStep: 'info',
      showMap: false
    });

    // 自动切换segment到基础信息
    this.switchToBasicSegment();
    
    return true;
  }

  /**
   * 切换到位置选择步骤
   */
  switchToLocationStep(): void {
    this.updateStepState({
      currentStep: 'location',
      showMap: true
    });

    // 自动切换segment到地图
    this.resetSegmentToMap();
  }

  /**
   * 返回位置选择步骤（重新选择位置）
   */
  backToLocationStep(): void {
    this.updateStepState({
      currentStep: 'location',
      showMap: true,
      locationConfirmed: false,
      canSwitchToInfo: false
    });

    // 自动切换segment到地图
    this.resetSegmentToMap();
  }

  /**
   * 处理segment变化
   */
  handleSegmentChange(segmentValue: SegmentValue): { success: boolean; message?: string } {
    const currentState = this.getCurrentStepState();

    if (segmentValue === 'map') {
      this.updateStepState({
        currentStep: 'location',
        showMap: true
      });
      return { success: true };
    } else if (segmentValue === 'basic') {
      // 只有在位置已确认的情况下才能切换到基础信息
      if (currentState.locationConfirmed) {
        this.updateStepState({
          currentStep: 'info',
          showMap: false
        });
        return { success: true };
      } else {
        // 如果位置未确认，重置segment到map
        this.resetSegmentToMap();
        return { 
          success: false, 
          message: '请先确认位置信息' 
        };
      }
    }

    return { success: false };
  }

  /**
   * 获取当前步骤
   */
  getCurrentStep(): StepType {
    return this.getCurrentStepState().currentStep;
  }

  /**
   * 检查位置是否已确认
   */
  isLocationConfirmed(): boolean {
    return this.getCurrentStepState().locationConfirmed;
  }

  /**
   * 检查是否显示地图
   */
  shouldShowMap(): boolean {
    return this.getCurrentStepState().showMap;
  }

  /**
   * 检查是否可以切换到信息步骤
   */
  canSwitchToInfoStep(): boolean {
    return this.getCurrentStepState().canSwitchToInfo;
  }

  /**
   * 获取动态标题
   */
  getDynamicTitle(modelMode: DetailsMode): string {
    const currentStep = this.getCurrentStep();
    if (currentStep === 'location') {
      return '位置采集';
    } else {
      return `关键点 ${modelMode}`;
    }
  }

  /**
   * 切换到基础信息segment
   */
  private switchToBasicSegment(): void {
    setTimeout(() => {
      const segment = document.querySelector('ion-segment') as any;
      if (segment) {
        segment.value = 'basic';
      }
    }, 100);
  }

  /**
   * 重置segment到地图选项
   */
  private resetSegmentToMap(): void {
    setTimeout(() => {
      const segment = document.querySelector('ion-segment') as any;
      if (segment) {
        segment.value = 'map';
      }
    }, 100);
  }

  /**
   * 更新步骤状态
   */
  private updateStepState(partialState: Partial<StepState>): void {
    const currentState = this.getCurrentStepState();
    this.stepState$.next({
      ...currentState,
      ...partialState
    });
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stepState$.complete();
  }
}
