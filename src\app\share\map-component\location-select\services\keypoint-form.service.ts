import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BehaviorSubject, Observable } from 'rxjs';
import { KeyPointInfo } from 'src/app/execut/class/key-point';
import { DetailsMode } from 'src/app/@core/base/environment';
import { CoordinateSystemService } from '../../service/coordinate-system.service';
import { Coordinate } from 'ol/coordinate';

/**
 * 表单状态
 */
export interface FormState {
  isValid: boolean;
  isDirty: boolean;
  errors: { [key: string]: any };
}

/**
 * 关键点表单服务
 * 负责表单创建、验证、数据准备等功能
 */
@Injectable({
  providedIn: 'root'
})
export class KeyPointFormService {
  private formGroup!: FormGroup;
  private formState$ = new BehaviorSubject<FormState>({
    isValid: false,
    isDirty: false,
    errors: {}
  });

  // 雨天不巡查状态
  private isItRaining$ = new BehaviorSubject<boolean>(false);

  constructor(
    private fb: FormBuilder,
    private coordinateSystemService: CoordinateSystemService
  ) {}

  /**
   * 获取表单组
   */
  getFormGroup(): FormGroup {
    return this.formGroup;
  }

  /**
   * 获取表单状态
   */
  getFormState(): Observable<FormState> {
    return this.formState$.asObservable();
  }

  /**
   * 获取雨天不巡查状态
   */
  getIsItRaining(): Observable<boolean> {
    return this.isItRaining$.asObservable();
  }

  /**
   * 设置雨天不巡查状态
   */
  setIsItRaining(value: boolean): void {
    this.isItRaining$.next(value);
  }

  /**
   * 创建表单
   */
  createForm(modelInfo: KeyPointInfo, taskCode: string): FormGroup {
    this.formGroup = this.fb.group({
      depCode: [modelInfo.depCode, [Validators.required]],
      depName: [modelInfo.depName],
      pointName: [modelInfo.pointName, [Validators.required]],
      inspectionMethod: [modelInfo.inspectionMethod, [Validators.required]],
      bufferRange: [modelInfo.bufferRange],
      isItRaining: [modelInfo.isItRaining],
      taskCode: [modelInfo.taskCode || taskCode],
    });

    // 监听表单状态变化
    this.formGroup.statusChanges.subscribe(() => {
      this.updateFormState();
    });

    this.formGroup.valueChanges.subscribe(() => {
      this.updateFormState();
    });

    // 初始化雨天不巡查状态
    this.isItRaining$.next(this.convertStringToBoolean(modelInfo.isItRaining));

    // 初始化表单状态
    this.updateFormState();

    return this.formGroup;
  }

  /**
   * 验证表单
   */
  validateForm(): boolean {
    if (!this.formGroup) {
      return false;
    }

    this.formGroup.markAllAsTouched();
    this.updateFormState();
    
    return this.formGroup.valid;
  }

  /**
   * 获取表单错误信息
   */
  getFormErrors(): { [key: string]: any } {
    if (!this.formGroup) {
      return {};
    }

    const errors: { [key: string]: any } = {};
    
    Object.keys(this.formGroup.controls).forEach(key => {
      const control = this.formGroup.get(key);
      if (control && control.errors && (control.dirty || control.touched)) {
        errors[key] = control.errors;
      }
    });

    return errors;
  }

  /**
   * 准备提交数据
   */
  prepareSubmitData(modelInfo: KeyPointInfo, coordinate: Coordinate): KeyPointInfo {
    if (!this.formGroup) {
      throw new Error('表单未初始化');
    }

    // 合并表单数据
    const formData = Object.assign({}, modelInfo, this.formGroup.value);
    
    // 设置雨天不巡查状态
    formData.isItRaining = this.convertBooleanToString(this.isItRaining$.value);
    
    // 设置坐标信息
    formData.pointGeom = coordinate;
    
    // 添加坐标系信息
    const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();
    (formData as any).coordinateSystem = currentCRS;

    console.log('📝 准备提交关键点数据:', {
      坐标: coordinate,
      坐标系: currentCRS,
      关键点名称: formData.pointName,
      表单数据: formData
    });

    return formData;
  }

  /**
   * 更新部门信息
   */
  updateDepartment(depInfo: { name: string }): void {
    if (this.formGroup) {
      this.formGroup.get('depName')?.setValue(depInfo.name);
    }
  }

  /**
   * 重置表单
   */
  resetForm(): void {
    if (this.formGroup) {
      this.formGroup.reset();
      this.updateFormState();
    }
  }

  /**
   * 初始化表单数据（用于编辑模式）
   */
  initializeFormData(modelInfo: KeyPointInfo, modelMode: DetailsMode): void {
    if (modelMode === DetailsMode.EDITE) {
      // 编辑模式下，设置雨天不巡查状态
      this.isItRaining$.next(this.convertStringToBoolean(modelInfo.isItRaining));
    }
  }

  /**
   * 将字符串转换为布尔值
   */
  convertStringToBoolean(value: string): boolean {
    return value === '是';
  }

  /**
   * 将布尔值转换为字符串
   */
  convertBooleanToString(value: boolean): string {
    return value ? '是' : '否';
  }

  /**
   * 更新表单状态
   */
  private updateFormState(): void {
    if (!this.formGroup) {
      return;
    }

    const state: FormState = {
      isValid: this.formGroup.valid,
      isDirty: this.formGroup.dirty,
      errors: this.getFormErrors()
    };

    this.formState$.next(state);
  }

  /**
   * 获取必填字段验证错误信息
   */
  getRequiredFieldErrors(): string[] {
    const errors: string[] = [];
    const formErrors = this.getFormErrors();

    if (formErrors['depCode']?.required) {
      errors.push('部门名称不能为空');
    }
    if (formErrors['pointName']?.required) {
      errors.push('关键点名称不能为空');
    }
    if (formErrors['inspectionMethod']?.required) {
      errors.push('巡检方式不能为空');
    }

    return errors;
  }

  /**
   * 检查特定字段是否有错误
   */
  hasFieldError(fieldName: string): boolean {
    const control = this.formGroup?.get(fieldName);
    return !!(control && control.errors && (control.dirty || control.touched));
  }

  /**
   * 获取特定字段的错误信息
   */
  getFieldError(fieldName: string): any {
    const control = this.formGroup?.get(fieldName);
    if (control && control.errors && (control.dirty || control.touched)) {
      return control.errors;
    }
    return null;
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.formState$.complete();
    this.isItRaining$.complete();
  }
}
